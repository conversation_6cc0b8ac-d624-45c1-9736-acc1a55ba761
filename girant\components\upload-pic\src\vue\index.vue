<script setup lang="ts">
/**
 * @component UploadPic
 * @description 头像上传组件
 * <AUTHOR>
 * @date [2025-7-11]
 */

import type {
  UploadFile,
  UploadFiles,
  UploadProps,
  UploadRequestOptions,
  UploadUserFile,
} from 'element-plus';

import type { PropType } from 'vue';

import { computed, onMounted, ref, watch } from 'vue';

import { CustomSnowflake, defaultRequestClient } from '@girant/utils';
import {
  ElIcon,
  ElImage,
  ElImageViewer,
  ElMessage,
  ElUpload,
} from 'element-plus';

/** 根据附件流水号获取文件列表类型 */
type getFileListType = {
  /** 文件id */
  fileId: string;
  /**	文件大小 */
  fileSize: number;
  /** 文件类型 */
  fileType: string;
  /** 文件类型名称 */
  fileTypeLabel: string;
  /**	是否图片 */
  isImg: boolean;
  /** 是否缩略图 */
  isThumb: boolean;
  /** 文件名 */
  name: string;
  /** 原始文件名 */
  originalName: string;
  /** 附件流水号 */
  serialNumber: string;
};
const props = defineProps({
  /** 是否自动上传 */
  autoUpload: {
    default: true,
    type: Boolean,
  },
  /** 文件大小限制 */
  fileSize: {
    default: 1,
    type: Number,
  },
  /** 限制上传图片数量(0为不限制，设置如果大于1，说明开启多图片上传) */
  limit: {
    default: 1,
    type: Number,
  },
  /** 回显图片id(优先级高于流水号)(支持传入数组) */
  imgId: {
    default: null,
    type: [String, Array] as PropType<string | string[]>,
  },
  /** 传入的流水号19位
   * （如果没有传入，则由组件生成）
   * （在上传时会附带，在编辑时获取图片列表回显）
   */
  serialNumber: {
    default: '',
    type: String,
  },
  /** 是否支持多选文件(选择上传图片时) */
  multiple: {
    default: false,
    type: Boolean,
  },
  /** 是否发送请求获取图片（如果填入false则不获取图片回显） */
  isLoadImgs: {
    default: true,
    type: Boolean,
  },
  /** 是否显示删除按钮 */
  showDeleteBtn: {
    default: true,
    type: Boolean,
  },
  /** 文件请求路径配置*/
  urlConfig: {
    default: () => ({
      uploadFileUrl: '',
      previewUrl: '',
      getImgListUrl: '',
      deleteFileUrl: '',
    }),
    type: Object,
  },
  /** 文本配置 */
  textConfig: {
    default: () => ({
      promptText: '',
      tipText: '',
    }),
    type: Object,
  },
  /** 图片适应容器策略fit 可选择contain、cover、fill、none、scale-down*/
  imgFit: {
    default: 'cover',
    type: String as PropType<
      '' | 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
    >,
  },
  /** 图片props */
  imgProps: {
    default: () => ({}),
    type: Object,
  },
  /** 图片宽度*/
  imgWidth: {
    default: 160,
    type: Number,
  },
  /** 图片高度*/
  imgHeight: {
    default: 160,
    type: Number,
  },
});
const emits = defineEmits([
  'imgUploadSuccess',
  'imgDeleteSuccess',
  'imgListDeleteSuccess',
  'update:serialNumber',
  'update:imgId',
]);
/** 不再上传 */
const noUpload = ref(false);
/** 发送请求的客户端*/
const request = defaultRequestClient;
/** 上传组件实例*/
const uploadRef = ref<InstanceType<typeof ElUpload>>();
/** 图片列表 */
const fileList = ref<UploadUserFile[]>([]);
/** 预览控制 */
const showPreview = ref(false);
/** 当前预览图片index */
const previewIndex = ref(0);

/** 当前使用的请求地址配置 */
const currentUrlConfig = computed(() => {
  return {
    uploadFileUrl:
      props.urlConfig.uploadFileUrl || '/file-manage/view/v1/file/upload',
    previewUrl:
      props.urlConfig.previewUrl || '/file-manage/view/v1/file/preview',
    getImgListUrl:
      props.urlConfig.getImgListUrl || '/file-manage/view/v1/file/getFileList',
    deleteFileUrl:
      props.urlConfig.deleteFileUrl || '/file-manage/view/v1/file/remove',
  };
});

/** 当前使用的文本配置 */
const currentTextConfig = computed(() => {
  return {
    promptText: props.textConfig.promptText || '上传图片',
    tipText:
      props.textConfig.tipText ||
      `选择${props.limit}张大小在1mb以内,格式为jpg、png的图片`,
  };
});

/** 当前使用的流水号 */
const currentSerialNumber = computed(() => {
  return props.serialNumber || new CustomSnowflake().nextId();
});

// 上传失败时钩子
const onError: UploadProps['onError'] = (
  error: Error,
  uploadFile: UploadFile,
  uploadFiles: UploadFiles,
) => {
  console.error('上传失败', error);
  ElMessage.error(`上传失败`);
  uploadFile.status = 'fail';
  uploadFiles.push(uploadFile); // 保留文件
  uploadFile.percentage = 0; // 重置进度条
};

/**
 * @function handleExceed
 * @description 文件超出个数限制钩子
 */
const handleExceed: UploadProps['onExceed'] = () => {
  ElMessage.warning(`只能上传${props.limit}个文件`);
};

/**
 * @function handleChange
 * @description 文件状态改变钩子
 * @param {UploadProps['onChange']} uploadFile - 上传的文件
 * @param {UploadProps['onChange']} uploadFiles - 上传的文件列表
 */
const handleChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
  const rawFile = uploadFile.raw;
  // 判断是否超出限制,控制新增是否显示 这里的是新增前的数量所以+1
  noUpload.value = fileList.value.length + 1 >= props.limit;
  // 判断文件类型
  if (!['image/jpeg', 'image/png'].includes(rawFile!.type)) {
    ElMessage.error('选择的文件格式有误');
    return uploadFiles.splice(uploadFiles.indexOf(uploadFile), 1);
  }
  // 判断文件大小
  else if (rawFile!.size > props.fileSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过${props.fileSize}MB!`);
    return uploadFiles.splice(uploadFiles.indexOf(uploadFile), 1);
  }
};

/** 移除文件时钩子 */
const handleRemove: UploadProps['onRemove'] = async (uploadFile) => {
  if (uploadFile.status === 'success') {
    try {
      // 要删除的图片id
      const fileId =
        (uploadFile?.response as getFileListType)?.fileId || uploadFile.name;
      // 发送请求删除文件
      await request.get(`${currentUrlConfig.value.deleteFileUrl}/${fileId}`);
      ElMessage.success('删除成功');
    } catch {
      ElMessage.error('删除失败');
    }
  }
  // 判断是否超出限制,控制新增是否显示 这里的是删除前的数量所以-1
  noUpload.value = fileList.value.length - 1 >= props.limit;
  emits('imgDeleteSuccess', uploadFile);
  // 获取图片id
  const res = await getImgId();
  emits('update:imgId', res);
  emits('update:serialNumber', currentSerialNumber.value);
};

/**
 * @function uploadFiles
 * @description 上传文件方法
 * @param {UploadRequestOptions} options - 要上传的内容
 */
const uploadFiles = async (options: UploadRequestOptions) => {
  const res = await request.upload<UploadRequestOptions>(
    currentUrlConfig.value.uploadFileUrl, // 上传地址
    {
      file: options.file, // 上传的文件
      serialNumber: currentSerialNumber.value, // 上传的文件流水号
    },
  );
  // 调用上传接口 并返回结果给上传组件
  return res;
};

/** 获取图片id */
const getImgId = () => {
  const res = fileList.value.map((item) => {
    return (item.response as getFileListType)?.fileId || item.name;
  });
  return res.length === 1 ? res[0] : res;
};

/** 上传完成钩子 */
const handleSuccess = async (response: getFileListType) => {
  emits('imgUploadSuccess', response);
  // 获取图片id
  const imgId = await getImgId();
  emits('update:imgId', imgId);
  emits('update:serialNumber', currentSerialNumber.value);
};

/**
 * @function uploadAllImg
 * @description 提交上传
 */
const uploadAllImg = async () => {
  if (uploadRef.value) {
    // 将失败的文件状态改为ready
    fileList.value.forEach((file) => {
      if (file.status === 'fail') {
        file.status = 'ready';
      }
    });
    uploadRef.value.submit(); // 调用上传组件提交上传
  }
};

/** 根据id获取图片信息 */
const getImgInfo = async (imgId: string | string[] = props.imgId) => {
  // 如果是数组
  if (Array.isArray(imgId)) {
    for (const id of imgId) {
      const res = await request.download(
        `${currentUrlConfig.value.previewUrl}/${id}?isThumb=true`,
      );
      if (res) {
        fileList.value.push({
          name: id,
          url: URL.createObjectURL(res),
        });
      }
    }
  }
  // 如果不是数组
  else {
    const res = await request.download(
      `${currentUrlConfig.value.previewUrl}/${imgId}?isThumb=true`,
    );
    if (res) {
      fileList.value.push({
        name: imgId,
        url: URL.createObjectURL(res),
      });
    }
  }
};

/** 根据流水号获取图片列表 */
const getImgInfoBySerialNumber = async (
  serialNumber: string = props.serialNumber,
) => {
  if (!serialNumber) return;
  // 获取文件信息列表
  const data = await request.get<getFileListType[]>(
    `${currentUrlConfig.value.getImgListUrl}/${props.serialNumber}`,
  );
  // 发送请求获取图片
  if (data && data.length > 0) {
    for (const item of data) {
      // 如果不是图片则跳过
      if (!item.isImg) continue;
      const res = await request.download(
        `${`${currentUrlConfig.value.previewUrl}/${item.fileId}`}?isThumb=true`,
      );
      if (res)
        fileList.value.push({
          url: URL.createObjectURL(res),
          name: item.fileId,
        });
    }
  }
};

/** 打开图片预览 */
const openPreview = (index: number) => {
  previewIndex.value = index;
  showPreview.value = true;
};

/** 发送请求获取图片 */
const getImgs = async () => {
  // 清空图片列表
  clearFileList();
  // 根据id获取图片
  if (props.imgId !== null) {
    await getImgInfo(props.imgId);
  }
  // 根据流水号获取图片
  else if (props.serialNumber) {
    await getImgInfoBySerialNumber(props.serialNumber);
  }
};

/** 清空图片列表 */
const clearFileList = () => {
  uploadRef.value?.clearFiles();
  emits('imgListDeleteSuccess');
  emits('update:imgId', null);
  emits('update:serialNumber', currentSerialNumber.value);
};

/** 监听图片id变化和流水号变化 */
watch(
  () => [props.imgId, props.serialNumber],
  async (newVal) => {
    if (newVal) {
      await getImgs();
      noUpload.value = fileList.value.length >= props.limit;
    }
  },
);

onMounted(async () => {
  if (props.isLoadImgs) {
    await getImgs();
    noUpload.value = fileList.value.length >= props.limit;
  }
});
defineExpose({
  /** 获取上传使用的流水号*/
  getSerialNumber: () => {
    return currentSerialNumber.value;
  },
  /** 获取全部图片列表 */
  getAllImgList: () => {
    return fileList.value;
  },
  /** 上传全部图片 */
  uploadAllImg,
  /** 清空图片列表 */
  clearFileList,
  /** 获取图片id */
  getImgId,
});
</script>
<template>
  <ElUpload
    class="img-uploader"
    :style="{
      '--img-width': `${imgWidth}px`,
      '--img-height': `${imgHeight}px`,
    }"
    :class="{ hide: fileList.length > 0 && noUpload }"
    v-bind="$attrs"
    v-model:file-list="fileList"
    ref="uploadRef"
    :action="currentUrlConfig.uploadFileUrl"
    :limit="limit"
    :multiple="multiple"
    list-type="picture-card"
    :auto-upload="autoUpload"
    :on-exceed="handleExceed"
    :on-change="handleChange"
    :on-remove="handleRemove"
    :http-request="uploadFiles"
    :on-success="handleSuccess"
    :on-error="onError"
  >
    <template #trigger>
      <slot name="trigger">
        {{ currentTextConfig.promptText }}
      </slot>
    </template>
    <template #default>
      <slot name="default"></slot>
    </template>
    <template #tip>
      <slot name="tip">
        <div class="el-upload__tip">
          {{ currentTextConfig.tipText }}
        </div>
      </slot>
    </template>
    <template #file="scope">
      <slot name="file" :scope="scope">
        <div class="relative h-full w-full">
          <ElImage
            class="h-full w-full"
            :src="scope.file.url"
            :fit="imgFit"
            v-bind="imgProps"
          />
          <!-- 角标 -->
          <div
            v-if="
              scope.file.status === 'success' || scope.file.status === 'fail'
            "
            :class="{
              'bg-success': scope.file.status === 'success',
              'bg-red-500': scope.file.status === 'fail',
            }"
            class="absolute right-[-15px] top-[-6px] inline-flex h-[24px] w-[40px] rotate-[45deg] items-center justify-center text-center"
          >
            <ElIcon
              color="#fff"
              size="12"
              class="absolute right-[0px] top-[5px] -rotate-[45deg]"
            >
              <Select v-if="scope.file.status === 'success'" />
              <CloseBold v-else />
            </ElIcon>
          </div>
        </div>
        <!-- 操作按钮 -->
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="openPreview(scope.index)"
          >
            <ElIcon><zoom-in /></ElIcon>
          </span>
          <span
            v-if="showDeleteBtn"
            class="el-upload-list__item-delete"
            @click="uploadRef?.handleRemove(scope.file)"
          >
            <ElIcon><delete /></ElIcon>
          </span>
        </span>
        <!-- 预览 -->
        <ElImageViewer
          v-if="showPreview && scope.index === previewIndex"
          :teleported="true"
          :url-list="
            fileList.length > 0
              ? (fileList.map((item) => item.url) as string[])
              : []
          "
          show-progress
          :initial-index="scope.index"
          @close="showPreview = false"
        />
      </slot>
    </template>
  </ElUpload>
</template>
<style>
/** 隐藏上传盒子 */
.hide .el-upload--picture-card {
  display: none !important;
}

/** 上传盒子 */
.img-uploader .el-upload--picture-card {
  width: var(--img-width) !important;
  height: var(--img-height) !important;
}

/** 图片盒子 */
.img-uploader .el-upload-list__item {
  width: var(--img-width) !important;
  height: var(--img-height) !important;
  margin: 0 5px 0 0 !important;
}
</style>
