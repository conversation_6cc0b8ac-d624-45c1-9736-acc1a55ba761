<script setup lang="ts">
/**
 * @component UploadPic
 * @description 头像上传组件
 * <AUTHOR>
 * @date [2025-7-11]
 */

import type {
  UploadProps,
  UploadRequestHandler,
  UploadRequestOptions,
} from 'element-plus';

import type { PropType } from 'vue';

import { onMounted, ref, watch } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { defaultRequestClient } from '@girant/utils';
import { ElMessage, ElUpload } from 'element-plus';

import '../css/index.css';

const props = defineProps({
  /** 上传地址 */
  action: {
    default: '/file-manage/view/v1/file/upload',
    type: String,
  },
  /** 预览地址 */
  previewUrl: {
    default: '/file-manage/view/v1/file/preview/',
    type: String,
  },
  /** 自定义上传函数 */
  customUpload: {
    default: undefined,
    type: Function as PropType<UploadRequestHandler>,
  },
  /** 文件大小限制 */
  fileSize: {
    default: 1,
    type: Number,
  },
  /** 上传区域提示文字 */
  text: {
    default: '上传头像',
    type: String,
  },
  /** 提示文字 */
  tip: {
    default: '选择一张大小在1mb以内,格式为jpg、png的图片',
    type: String,
  },
  /** 支持的文件格式 */
  listType: {
    default: () => ['image/jpeg', 'image/png'],
    type: Array,
  },
  /** 图片id */
  imgId: {
    default: '',
    type: String,
  },
});
/** 发送请求的客户端*/
const request = defaultRequestClient;
const uploadRef = ref<InstanceType<typeof ElUpload>>();
/** 图片路径 */
const imageUrl = ref('');
/** 上传成功后的返回值 */
const response = ref<any>();

/**
 * @function handleExceed
 * @description 文件超出个数限制钩子
 */
const handleExceed: UploadProps['onExceed'] = () => {
  if (uploadRef.value) {
    uploadRef.value.clearFiles(); // 调用上传组件清空文件
  }
  ElMessage.warning(`只能上传1个文件`);
};

/**
 * @function handleChange
 * @description 文件状态改变钩子
 * @param {UploadProps['onChange']} uploadFile - 上传的文件
 * @param {UploadProps['onChange']} uploadFiles - 上传的文件列表
 */
const handleChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
  const rawFile = uploadFile.raw;
  // 判断文件类型
  if (props.listType ? !props.listType.includes(rawFile!.type) : false) {
    ElMessage.error('选择的文件格式有误');
    return uploadFiles.splice(uploadFiles.indexOf(uploadFile), 1);
  }
  // 判断文件大小
  else if (rawFile!.size > props.fileSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过${props.fileSize}MB!`);
    return uploadFiles.splice(uploadFiles.indexOf(uploadFile), 1);
  }
  // 删除前一个文件 保持上传文件列表只有一个
  if (uploadFiles.length > 1) {
    uploadFiles.splice(uploadFiles.indexOf(uploadFile) - 1, 1);
  }
  // 本地待上传图片生成预览文件 如果已经上传成功则不生成
  if (uploadFile.status !== 'success' && rawFile) {
    imageUrl.value = URL.createObjectURL(rawFile);
  }
};

/**
 * @function uploadFiles
 * @description 上传文件方法
 * @param {UploadRequestOptions} options - 要上传的内容
 */
const uploadFiles = async (options: UploadRequestOptions) => {
  response.value = await request.upload<UploadRequestOptions>(
    props.action, // 上传地址
    {
      file: options.file, // 上传的文件
    },
  );
  // 调用上传接口 并返回结果给上传组件
  return response.value;
};

/**
 * @function submitUpload
 * @description 提交上传
 */
const submitUpload = async () => {
  if (uploadRef.value) {
    uploadRef.value.submit(); // 调用上传组件提交上传
  }
};

/** 根据id获取图片信息 */
const getImgInfo = async (imgId: string = props.imgId) => {
  const res = await request.download(
    `${props.previewUrl}${imgId}?isThumb=true`,
  );
  imageUrl.value = URL.createObjectURL(res);
};

/** 监听图片id变化 */
watch(
  () => props.imgId,
  async (newVal) => {
    if (newVal) {
      await getImgInfo();
    }
  },
);

onMounted(async () => {
  if (props.imgId) {
    await getImgInfo();
  }
});
defineExpose({
  response, // 上传成功后的返回值
  submitUpload, // 调用提交上传
  uploadRef, // 上传组件实例
  getImgInfo, // 根据id获取图片信息 传入id会自动获取
});
</script>
<template>
  <ElUpload
    v-bind="$attrs"
    ref="uploadRef"
    :drag="true"
    :action="action"
    :limit="2"
    :auto-upload="true"
    :show-file-list="false"
    :on-exceed="handleExceed"
    :on-change="handleChange"
    :http-request="customUpload || uploadFiles"
    class="upload-avatar mb-10"
  >
    <slot></slot>
    <template #tip>
      <slot name="tip">
        <div class="el-upload__tip">
          {{ tip }}
        </div>
      </slot>
    </template>
    <template #trigger>
      <slot name="trigger">
        <ImageViewer
          class="a flex justify-center"
          v-if="imageUrl"
          :src="imageUrl"
          :preview-src-list="undefined"
        />
        <span v-else>{{ text }}</span>
      </slot>
    </template>
  </ElUpload>
</template>
